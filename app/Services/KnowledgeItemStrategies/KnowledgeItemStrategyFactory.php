<?php

namespace App\Services\KnowledgeItemStrategies;

use App\Contracts\KnowledgeItemStrategyInterface;
use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Models\BailianArticle;
use App\Models\BailianDirectory;
use App\Models\BailianFile;
use InvalidArgumentException;

/**
 * 知识库项目策略工厂
 */
class KnowledgeItemStrategyFactory
{
    /**
     * 策略映射
     */
    private static array $strategies = [
        BailianArticle::class => ArticleKnowledgeStrategy::class,
        BailianFile::class => FileKnowledgeStrategy::class,
        BailianDirectory::class => DirectoryKnowledgeStrategy::class,
    ];

    /**
     * 策略实例缓存
     */
    private static array $instances = [];

    /**
     * 根据模型实例创建策略
     *
     * @param mixed $model 模型实例
     * @return KnowledgeItemStrategyInterface
     */
    public static function createForModel($model): KnowledgeItemStrategyInterface
    {
        // 对于文章模型，根据类型选择不同的策略
        if ($model instanceof BailianArticle) {
            return self::createForArticle($model);
        }

        $modelClass = get_class($model);
        return self::createForClass($modelClass);
    }

    /**
     * 根据模型类名创建策略
     *
     * @param string $modelClass 模型类名
     * @return KnowledgeItemStrategyInterface
     */
    public static function createForClass(string $modelClass): KnowledgeItemStrategyInterface
    {
        if (!isset(self::$strategies[$modelClass])) {
            throw new InvalidArgumentException("不支持的模型类型: {$modelClass}");
        }

        $strategyClass = self::$strategies[$modelClass];

        // 使用单例模式缓存策略实例
        if (!isset(self::$instances[$strategyClass])) {
            self::$instances[$strategyClass] = new $strategyClass();
        }

        return self::$instances[$strategyClass];
    }

    /**
     * 根据模型类型字符串创建策略
     *
     * @param string $modelType 模型类型（如 'bailian_article'）
     * @return KnowledgeItemStrategyInterface
     */
    public static function createForType(string $modelType): KnowledgeItemStrategyInterface
    {
        $modelClass = self::getModelClassByType($modelType);
        return self::createForClass($modelClass);
    }

    /**
     * 获取所有支持的模型类型
     *
     * @return array
     */
    public static function getSupportedModelClasses(): array
    {
        return array_keys(self::$strategies);
    }

    /**
     * 注册新的策略
     *
     * @param string $modelClass 模型类名
     * @param string $strategyClass 策略类名
     */
    public static function register(string $modelClass, string $strategyClass): void
    {
        self::$strategies[$modelClass] = $strategyClass;
    }

    /**
     * 根据模型类型字符串获取模型类名
     *
     * @param string $modelType
     * @return string
     */
    private static function getModelClassByType(string $modelType): string
    {
        $typeToClassMap = [
            'bailian_article' => BailianArticle::class,
            'bailian_file' => BailianFile::class,
            'bailian_directory' => BailianDirectory::class,
        ];

        if (!isset($typeToClassMap[$modelType])) {
            throw new InvalidArgumentException("不支持的模型类型: {$modelType}");
        }

        return $typeToClassMap[$modelType];
    }

    /**
     * 根据文章类型创建策略
     *
     * @param BailianArticle $article
     * @return KnowledgeItemStrategyInterface
     */
    private static function createForArticle(BailianArticle $article): KnowledgeItemStrategyInterface
    {
        // 根据文章类型选择策略
        $strategyClass = match ($article->type) {
            BailianArticleTypeEnum::MEETING => MeetingKnowledgeStrategy::class,
            default => ArticleKnowledgeStrategy::class,
        };

        // 使用单例模式缓存策略实例
        if (!isset(self::$instances[$strategyClass])) {
            self::$instances[$strategyClass] = new $strategyClass();
        }

        return self::$instances[$strategyClass];
    }
}
