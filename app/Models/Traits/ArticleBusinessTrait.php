<?php

namespace App\Models\Traits;

use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Jobs\BaiLian\ArticleUpdateJob;
use App\Models\BailianArticle;
use App\Models\BailianTag;
use App\Services\KnowledgeItemStrategies\KnowledgeItemStrategyFactory;
use App\Services\UploadService;
use DB;
use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\UploadedFile;
use Log;
use Modules\Storage\Models\Upload;
use Storage;

trait ArticleBusinessTrait
{
    protected static function bootArticleBusinessTrait()
    {
        self::deleted(function ($model) {
            // 区分主笔记和子笔记的删除逻辑
            if ($model->isMainArticle()) {
                // 主笔记删除：如果在知识库中，从知识库移除
                if ($model->isInKnowledge()) {
                    try {
                        $strategy = KnowledgeItemStrategyFactory::createForModel($model);
                        $strategy->removeFromKnowledge($model);
                    } catch (\Exception $e) {
                        Log::error('删除主笔记时清理知识库数据失败', [
                            'article_id' => $model->id,
                            'storage_id' => $model->storage_id,
                            'error'      => $e->getMessage()
                        ]);
                    }
                }

                // 删除所有子笔记
                foreach ($model->children as $child) {
                    $child->delete();
                }
            } else {
                // 子笔记删除：如果主笔记在知识库中，异步更新主笔记的知识库内容
                $parentToUpdate = $model->getParentToUpdateAfterDelete();
                if ($parentToUpdate) {
                    // 使用 DB::afterCommit 确保在事务提交后再触发异步任务
                    DB::afterCommit(function () use ($parentToUpdate) {
                        ArticleUpdateJob::dispatch($parentToUpdate);
                    });
                }
            }
        });
    }

    public function setContentAttribute($value)
    {
        if (is_string($value)) {
            $this->attributes['content'] = $value;
        } elseif (is_array($value)) {
            $this->attributes['content'] = json_encode($value, JSON_UNESCAPED_UNICODE);
        } else {
            $this->attributes['content'] = $value;
        }
    }

    /**
     * Notes: 获取文章的上级文章
     *
     * @Author: 玄尘
     * @Date: 2025/5/8 10:10
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * Notes: 获取文章的追加文章
     *
     * @Author: 玄尘
     * @Date: 2025/5/8 10:10
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * 获取文章关联的标签
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(
            BailianTag::class,
            'bailian_article_tags',
            'article_id',
            'tag_id',
        );
    }

    public function getContent()
    {
        return $this->content;
    }

    /**
     * 将笔记内容转换为Markdown文件
     * 如果是主笔记，会合并所有子笔记内容
     *
     * @return string 临时文件路径
     */
    public function prepareStorage(): string
    {
        try {
            // 如果已有存储文件，先删除旧文件
            if ($this->storage_id) {
                $oldUpload = Upload::find($this->storage_id);
                Storage::delete($oldUpload->path);
                $oldUpload->delete();
            }
        } catch (Exception $e) {
            Log::error('删除笔记时清理知识库数据失败', [
                'article_id' => $this->id,
            ]);
        }

        // 创建临时文件
        $tempFilePath = storage_path('app/temp/'.uniqid('bailian_article_').'.md');

        // 确保目录存在
        $directory = dirname($tempFilePath);
        if (! is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        // 构建Markdown内容
        $markdownContent = $this->buildMarkdownContent();

        // 写入文件
        file_put_contents($tempFilePath, $markdownContent);

        // 创建 UploadedFile 对象,filename 命名规则 id_title.md
        $fileName     = $this->getTitle().'.md';
        $uploadedFile = new UploadedFile(
            $tempFilePath,
            $fileName,
            'text/markdown',
            null,
            true // 测试模式，不检查上传过程
        );

        // 上传文档到存储
        $uploadService = app(UploadService::class);
        $uploadService->useOriginalName(true);
        $uploadService->setPath('bailian/article');
        $result = $uploadService->save($uploadedFile);

        // 查找并更新用户ID
        $upload = Upload::where('hash', $result['hash'])->first();
        if ($upload && $this->user_id && $upload->user_id != $this->user_id) {
            $upload->update(['user_id' => $this->user_id]);
            $upload->refresh();
        }

        // 更新笔记的storage_id
        $this->storage_id = $upload->id;
        $this->save();

        // 删除临时文件
        @unlink($tempFilePath);

        return $tempFilePath;
    }

    /**
     * 构建Markdown内容
     * 主笔记会包含所有子笔记内容
     *
     * @return string
     */
    protected function buildMarkdownContent(): string
    {
        $title   = $this->getTitle() ?: '文档';
        $content = $this->getContentData();

        // 构建主笔记内容
        $markdownContent = "# {$title}\n\n{$content}";

        // 如果是主笔记，添加所有子笔记内容
        if (is_null($this->parent_id)) {
            $children = $this->children()->orderBy('created_at')->get();

            foreach ($children as $child) {
                $childTitle   = $child->getTitle() ?: '子笔记';
                $childContent = $child->getContentData();

                $markdownContent .= "\n\n## {$childTitle}\n\n{$childContent}";
            }
        }

        return $markdownContent;
    }

    /**
     * 判断是否为主笔记
     *
     * @return bool
     */
    public function isMainArticle(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * 判断是否为子笔记
     *
     * @return bool
     */
    public function isChildArticle(): bool
    {
        return ! is_null($this->parent_id);
    }

    /**
     * Notes: 检查内容变更后是否需要更新知识库
     *
     * @Author: 玄尘
     * @Date: 2025/6/6 20:25
     */
    public function getArticleToUpdateInKnowledge(): ?BailianArticle
    {
        // 如果是主笔记且在知识库中，返回自己
        if ($this->isMainArticle() && $this->isInKnowledge()) {
            return $this;
        }

        // 如果是子笔记且主笔记在知识库中，返回主笔记
        if ($this->isChildArticle() && $this->parent && $this->parent->isInKnowledge()) {
            return $this->parent;
        }

        return null;
    }

    /**
     * 检查删除后是否需要更新主笔记的知识库
     * 返回需要更新的主笔记实例
     *
     * @return BailianArticle|null
     */
    public function getParentToUpdateAfterDelete(): ?BailianArticle
    {
        if ($this->isChildArticle() && $this->parent && $this->parent->isInKnowledge()) {
            return $this->parent;
        }

        return null;
    }

    public function getFileTypes(): array
    {
        return [
            'extension' => '',
            'mimeType'  => '',
            'icon'      => $this->type->value,
            'icon_type' => $this->type->value,
            'name'      => '',
        ];
    }

    /**
     * Notes: 统计字数
     *
     * @Author: 玄尘
     * @Date: 2025/5/28 15:52
     * @return int
     */
    public function getWordCount(): int
    {
        $content = $this->getContentData();
        // 移除HTML标签并计算纯文本字数
        $textContent = strip_tags($content);

        // 使用mb_strlen确保正确处理中文字符
        return mb_strlen($textContent, 'UTF-8');
    }

    public function getArticleDescription()
    {
        // 为标题生成使用，不显示【链接】【图片】等标记，直接提取纯文本
        return $this->extractPlainText();
    }

    /**
     * 提取纯文本内容，用于标题生成等场景
     * 不显示【链接】【图片】等标记
     *
     * @return string
     */
    private function extractPlainText(): string
    {
        $data = $this->getContentData();
        if (empty($data)) {
            return '';
        }

        $markdown = trim($data, '"');
        $text     = str_replace(['\\n', '\n'], "\n", $markdown);

        // 1. 删除所有图片标记 ![alt](url)
        $markdown = preg_replace('/!\[.*?\]\(.*?\)/', '', $text);

        // 2. 删除链接但保留链接文字 [text](url) -> text
        $markdown = preg_replace('/\[([^\]]*)\]\([^\)]*\)/', '$1', $markdown);

        // 3. 删除代码块 ```code```
        $markdown = preg_replace('/```[\s\S]*?```/', '', $markdown);

        // 4. 删除行内代码 `code`
        $markdown = preg_replace('/`([^`]*)`/', '', $markdown);

        // 5. 删除标题标记 # ## ### 等
        $markdown = preg_replace('/^#{1,6}\s*/m', '', $markdown);

        // 6. 删除粗体和斜体标记，保留文字内容
        $markdown = preg_replace('/\*\*([^\*]*)\*\*/', '$1', $markdown);
        $markdown = preg_replace('/__([^_]*)__/', '$1', $markdown);
        $markdown = preg_replace('/(?<!\*)\*([^\*\n]+)\*(?!\*)/', '$1', $markdown);
        $markdown = preg_replace('/(?<!_)_([^_\n]+)_(?!_)/', '$1', $markdown);

        // 7. 删除列表标记（保留列表内容）
        $markdown = preg_replace('/^\s*[-\*\+]\s+/m', '', $markdown);
        $markdown = preg_replace('/^\s*\d+\.\s+/m', '', $markdown);

        // 8. 删除引用标记 > quote
        $markdown = preg_replace('/^\s*>\s*/m', '', $markdown);

        // 9. 删除水平分割线
        $markdown = preg_replace('/^\s*[-\*_]{3,}\s*$/m', '', $markdown);

        // 10. 删除表格分隔符（保留表格内容）
        $markdown = preg_replace('/^\s*\|?[-\s\|:]+\|?\s*$/m', '', $markdown);
        $markdown = preg_replace('/^\s*\|/m', '', $markdown);
        $markdown = preg_replace('/\|\s*$/m', '', $markdown);
        $markdown = str_replace('|', ' ', $markdown);

        // 11. 删除转义字符
        $markdown = preg_replace('/\\\\(.)/', '$1', $markdown);

        // 12. 清理多余的空白字符
        $markdown = preg_replace('/\n\s*\n\s*\n/', "\n\n", $markdown);
        $markdown = preg_replace('/[ \t]+/', ' ', $markdown);
        $markdown = preg_replace('/\n+/', ' ', $markdown);
        $markdown = trim($markdown);

        // 13. 截取200字符
        $markdown = mb_substr($markdown, 0, 200);

        return trim($markdown);
    }

    public function getContentData()
    {
        $content = $this->content;

        // 处理不同的 content 格式
        if (is_array($content) && isset($content['data'])) {
            $data = $content['data'];
        } elseif (is_string($content)) {
            $decoded = json_decode($content, true);
            if (json_last_error() === JSON_ERROR_NONE && isset($decoded['data'])) {
                $data = $decoded['data'];
            } else {
                $data = $content;
            }
        } else {
            $data = '';
        }
        return $data;
    }

    public static function getContentDataByInput($type, $input)
    {
        return match ($type) {
            BailianArticleTypeEnum::ARTICLE->value => ['data' => $input['content']['data'] ?? ''],
            BailianArticleTypeEnum::IMAGE->value => [
                'image' => $input['content']['image'] ?? '',
                'data'  => $input['content'] ?? ''
            ],
            BailianArticleTypeEnum::RECORD->value => [
                'audio'         => $input['content']['audio'] ?? '',
                'audioDuration' => (int) ($input['content']['audioDuration'] ?? 0),
                'data'          => $input['content']['data'] ?? ''
            ],
            BailianArticleTypeEnum::MEETING->value => [
                'audio'         => $input['content']['audio'] ?? '',
                'audioDuration' => (int) ($input['content']['audioDuration'] ?? 0),
                'data'          => $input['content']['data'] ?? '',
                'text_record'   => $input['content']['text_record'] ?? ''
            ],
            BailianArticleTypeEnum::LINK->value => [
                'link'      => $input['content']['link'] ?? '',
                'linkTitle' => $input['content']['linkTitle'] ?? '',
                'linkImage' => $input['content']['linkImage'] ?? '',
                'data'      => $input['content']['data'] ?? ''
            ],
            default => []
        };
    }

    /**
     * Notes: 设置标签
     *
     * @Author: 玄尘
     * @Date: 2025/5/30 18:54
     * @param $user
     * @param  string  $tags
     * @return bool
     * @throws \Throwable
     */
    public function setTags($user, array|string $tags): bool
    {
        if (empty($tags)) {
            return true;
        }

        if (is_string($tags)) {
            // 将中文逗号替换为英文逗号，并分割成数组
            $tags = str_replace('，', ',', $tags);
            $tags = array_filter(array_map('trim', explode(',', $tags)));
        }
        $hasTagIds = $this->tags()->pluck('bailian_tags.id')->toArray();
        try {
            DB::beginTransaction();
            // 使用事务确保数据一致性
            $tagIds = [];

            foreach ($tags as $tag) {
                // 创建或更新标签
                $tagModel = $user->tags()->updateOrCreate([
                    'name' => $tag
                ]);

                $tagIds[] = $tagModel->id;
            }
            $insertIds = array_diff($tagIds, $hasTagIds);
            // 同步标签关系
            if (! empty($insertIds)) {
                // 这里是附加
                $this->tags()->attach($tagIds);
            }
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();

            return false;
        }
    }

    /**
     * 提取当前笔记中的所有图片URL
     * 优先使用image字段，如果没有则从data字段的markdown内容中提取
     * 返回缩略图URL，如果缩略图不存在则自动生成
     *
     * @return array 去重后的缩略图URL数组
     */
    public function getImages(): array
    {
        // 获取当前笔记的图片
        $images = $this->extractImagesFromArticle();
        // 去重并过滤空值
        $images = array_filter(array_unique($images));

        // 转换为缩略图URL
        $uploadService = app(UploadService::class);
        $thumbnailUrls = [];

        $i = 0;
        foreach ($images as $imageUrl) {
            if ($i >= 5) {
                break;
            }
            $thumbnailUrls[] = $uploadService->getThumbnailUrl($imageUrl);
            $i++;
        }

        return array_values($thumbnailUrls);
    }

    /**
     * 从笔记内容中提取图片URL
     * 优先使用image字段，如果没有则从data字段的markdown内容中提取
     *
     * @return array
     */
    protected function extractImagesFromArticle(): array
    {
        $images  = [];
        $content = $this->content;

        // 处理不同格式的content数据
        if (is_string($content)) {
            $content = json_decode($content, true) ?? [];
        }

        // 优先检查是否有image字段
        if (! empty($content['image'])) {
            $imageUrls = explode(',', $content['image']);
            $imageUrls = array_map('trim', $imageUrls);
            // 过滤出真正的图片文件
            $imageUrls = array_filter($imageUrls, function ($url) {
                return UploadService::checkIsImageUrl($url);
            });
            $images    = array_merge($images, $imageUrls);
        } else {
            //如果没有image字段，从markdown内容中提取图片
            $markdownContent = $content['data'] ?? '';
            if ($markdownContent) {
                preg_match_all('/!\[.*?\]\((.*?)\)/', $markdownContent, $matches);
                if (! empty($matches[1])) {
                    // 过滤出真正的图片文件，排除音频、视频等其他文件
                    $imageUrls = array_filter($matches[1], function ($url) {
                        return UploadService::checkIsImageUrl($url);
                    });
                    $images    = array_merge($images, $imageUrls);
                }
            }
        }

        // 过滤空值和无效URL
        return array_filter($images, function ($url) {
            return ! empty($url) && is_string($url) && trim($url) !== '';
        });
    }

    /**
     * Notes: 重新生成资源内容
     *
     * @Author: 玄尘
     * @Date: 2025/6/13 14:42
     * @return mixed
     */
    public function getResourceContent(): mixed
    {
        $content = $this->content;
        $images  = $this->getImages();
        if (! empty($images)) {
            $content['image'] = implode(',', $images);
        }
        return $content;
    }

    /**
     * 生成文章简介描述
     * 从 content->data 中提取内容，将链接替换成【链接】，识别图片音频视频
     *
     * @return string
     */
    public function generateDescription($data = null): string
    {
        if (! $data) {
            $data = $this->getContentData();
        }

        if (empty($data)) {
            return '';
        }

        $markdown = trim($data, '"');
        $text     = str_replace(['\\n', '\n'], "\n", $markdown);

        // 1. 识别并替换图片 ![alt](url) -> 【图片】
        $markdown = preg_replace('/!\[.*?\]\([^)]*\.(jpg|jpeg|png|gif|webp|bmp|svg)[^)]*\)/i', '【图片】', $text);

        // 2. 识别并替换音频文件 ![alt](url) -> 【音频】
        $markdown = preg_replace('/!\[.*?\]\([^)]*\.(mp3|wav|flac|aac|ogg|m4a|wma)[^)]*\)/i', '【音频】', $markdown);

        // 3. 识别并替换视频文件 ![alt](url) -> 【视频】
        $markdown = preg_replace('/!\[.*?\]\([^)]*\.(mp4|avi|mov|wmv|flv|webm|mkv|m4v)[^)]*\)/i', '【视频】', $markdown);

        // 4. 处理剩余的图片标记（没有文件扩展名的）
        $markdown = preg_replace('/!\[.*?\]\(.*?\)/', '【图片】', $markdown);

        // 5. 将所有链接替换成【链接】[text](url) -> 【链接】
        $markdown = preg_replace('/\[([^\]]*)\]\([^\)]*\)/', '【链接】', $markdown);

        // 6. 删除代码块 ```code```
        $markdown = preg_replace('/```[\s\S]*?```/', '', $markdown);

        // 7. 删除行内代码 `code`
        $markdown = preg_replace('/`([^`]*)`/', '', $markdown);

        // 8. 删除标题标记 # ## ### 等
        $markdown = preg_replace('/^#{1,6}\s*/m', '', $markdown);

        // 9. 删除粗体和斜体标记，保留文字内容
        $markdown = preg_replace('/\*\*([^\*]*)\*\*/', '$1', $markdown);
        $markdown = preg_replace('/__([^_]*)__/', '$1', $markdown);
        $markdown = preg_replace('/(?<!\*)\*([^\*\n]+)\*(?!\*)/', '$1', $markdown);
        $markdown = preg_replace('/(?<!_)_([^_\n]+)_(?!_)/', '$1', $markdown);

        // 10. 删除列表标记（保留列表内容）
        $markdown = preg_replace('/^\s*[-\*\+]\s+/m', '', $markdown);
        $markdown = preg_replace('/^\s*\d+\.\s+/m', '', $markdown);

        // 11. 删除引用标记 > quote
        $markdown = preg_replace('/^\s*>\s*/m', '', $markdown);

        // 12. 删除水平分割线
        $markdown = preg_replace('/^\s*[-\*_]{3,}\s*$/m', '', $markdown);

        // 13. 删除表格分隔符（保留表格内容）
        $markdown = preg_replace('/^\s*\|?[-\s\|:]+\|?\s*$/m', '', $markdown);
        $markdown = preg_replace('/^\s*\|/m', '', $markdown);
        $markdown = preg_replace('/\|\s*$/m', '', $markdown);
        $markdown = str_replace('|', ' ', $markdown);

        // 14. 删除转义字符
        $markdown = preg_replace('/\\\\(.)/', '$1', $markdown);

        // 15. 清理多余的空白字符
        $markdown = preg_replace('/\n\s*\n\s*\n/', "\n\n", $markdown);
        $markdown = preg_replace('/[ \t]+/', ' ', $markdown);
        $markdown = preg_replace('/\n+/', ' ', $markdown);
        $markdown = trim($markdown);

        // 16. 截取200字符
        $markdown = mb_substr($markdown, 0, 200);

        return trim($markdown);
    }

    //去除html标签
    public function stripHtmlTagsOfContent()
    {
        $content         = $this->content;
        $data            = $this->getContentData();
        $data            = strip_tags($data);
        $content['data'] = $data;
        return $content;
    }

    /**
     * Notes: 获取去除html标签后的内容
     *
     * @Author: 玄尘
     * @Date: 2025/6/23 17:42
     */
    public function getStripHtmlData()
    {
        $content = $this->stripHtmlTagsOfContent();
        return $content['data'] ?? '';
    }

    //返回会议笔记都有什么，判断 是否 有智能总结 是否有章节概括  是否有待办事项
    public function getMeetingSummaryStatus()
    {
        // 如果不是会议笔记，返回空状态
        if ($this->type !== \App\Enums\Bailian\BailianArticleTypeEnum::MEETING) {
            return [
                'has_summary' => false,
                'has_agents' => false,
                'has_paragraphs' => false,
            ];
        }

        $content = $this->content;
        if (is_string($content)) {
            $content = json_decode($content, true);
        }

        $summary = $content['data'] ?? '';

        return [
            'has_summary' => $this->hasValidMeetingSummary($summary),
            'has_agents' => $this->hasValidMeetingAgents($summary),
            'has_paragraphs' => $this->hasValidMeetingParagraphs($summary),
        ];
    }

    /**
     * 检查是否有有效的会议总结
     */
    private function hasValidMeetingSummary(string $summary): bool
    {
        if (empty($summary)) {
            return false;
        }

        // 去除HTML标签和空白字符
        $cleanSummary = trim(strip_tags($summary));

        // 检查是否只是简单的重复内容（如"喂"、"测试"等）
        if (strlen($cleanSummary) < 10) {
            return false;
        }

        // 检查是否包含会议总结的关键标识
        return strpos($summary, '## 会议总结') !== false ||
               strpos($summary, '## 会议要点') !== false;
    }

    /**
     * 检查是否有有效的待办事项
     */
    private function hasValidMeetingAgents(string $summary): bool
    {
        if (empty($summary)) {
            return false;
        }

        // 检查是否包含待办事项部分且不是"无待办"
        if (strpos($summary, '## 待办事项') === false) {
            return false;
        }

        // 检查是否不是"无待办"
        return strpos($summary, '- 无待办') === false;
    }

    /**
     * 检查是否有有效的章节摘要
     */
    private function hasValidMeetingParagraphs(string $summary): bool
    {
        if (empty($summary)) {
            return false;
        }

        // 检查是否包含章节摘要部分
        if (strpos($summary, '## 章节摘要') === false) {
            return false;
        }

        // 检查章节摘要是否有实际内容（不只是简单重复）
        preg_match_all('/##### \d+ .+?\n\n(.+?)\n\n/s', $summary, $matches);

        if (empty($matches[1])) {
            return false;
        }

        // 检查是否有多个不同的章节或内容足够丰富
        $contents = array_map('trim', $matches[1]);
        $uniqueContents = array_unique($contents);

        return count($uniqueContents) > 1 || strlen(implode('', $contents)) > 20;
    }

}
