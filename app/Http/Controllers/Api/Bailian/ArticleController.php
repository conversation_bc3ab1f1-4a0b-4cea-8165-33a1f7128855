<?php

namespace App\Http\Controllers\Api\Bailian;

use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Enums\Bailian\ItemSyncStatusEnum;
use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Requests\Bailian\Article\AddToKnowledgeRequest;
use App\Http\Requests\Bailian\Article\BatchAddToKnowledgeRequest;
use App\Http\Requests\Bailian\Article\CreateArticleRequest;
use App\Http\Requests\Bailian\Article\DeleteTagRequest;
use App\Http\Requests\Bailian\Article\SetTagRequest;
use App\Http\Requests\Bailian\Article\UpdateArticleRequest;
use App\Http\Resources\Bailian\Article\ArticleCollection;
use App\Http\Resources\Bailian\Article\ArticleResource;
use App\Http\Resources\Bailian\Tag\TagResource;
use App\Jobs\BaiLian\ArticleUpdateJob;
use App\Jobs\BaiLian\GenerateArticleTagsJob;
use App\Jobs\BaiLian\SyncKnowledgeItemToAlibabaJob;
use App\Models\BailianArticle;
use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeItem;
use App\Models\BailianTag;
use App\Services\KnowledgeItemStrategies\KnowledgeItemStrategyFactory;
use App\Services\MeetingSummaryService;
use DB;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ArticleController extends ApiController
{
    /**
     * 获取文章列表
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $user     = $request->kernel->user();
        $title    = $request->title ?? '';
        $type     = $request->type ?? '';
        $order_by = $request->order_by ?? 'created_at';
        $parentId = $request->parent_id ?? null;
        $pagesize = $request->pagesize ?? 15;
        $sortType = $request->sort_type ?? 'desc';

        $articles = BailianArticle::query()
            ->ofUser($user)
            ->when($title, function ($query) use ($title) {
                return $query->where(function ($subQuery) use ($title) {
                    $subQuery->where('title', 'like', "%{$title}%")
                        ->orWhere('content', 'like', "%{$title}%");
                });
            })
            ->when($type, function ($query) use ($type) {
                return $query->where('type', $type);
            })
            ->when($parentId !== null, function ($query) use ($parentId) {
                return $query->where('parent_id', $parentId);
            }, function ($query) {
                // 当没有指定 parent_id 时，只获取根文章
                return $query->whereNull('parent_id');
            })
            ->orderBy($order_by, strtolower($sortType))
            ->withCount('children')
            ->paginate($pagesize);

        return $request->kernel->success(new ArticleCollection($articles));
    }

    /**
     * 获取文章详情
     *
     * @param  Request  $request
     * @return JsonResponse
     * @throws ValidatorException
     */
    public function detail(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'id' => ['required', 'exists:bailian_articles,id'],
        ], [
            'id.required' => '笔记ID不能为空',
            'id.exists'   => '笔记不存在',
        ]);

        $id   = $request->id;
        $user = $request->kernel->user();

        $article = BailianArticle::find($id);

        // 增加浏览量但不更新 updated_at 字段
        DB::table('bailian_articles')
            ->where('id', $id)
            ->increment('browse_count');

        return $request->kernel->success(new ArticleResource($article));
    }

    /**
     * 创建文章
     *
     * @param  CreateArticleRequest  $request
     * @return JsonResponse|StreamedResponse
     */
    public function create(CreateArticleRequest $request)
    {
        try {
            DB::beginTransaction();
            $user            = $request->kernel->user();
            $title           = $request->title;
            $content         = $request->details;
            $type            = $request->type;
            $parentId        = $request->parent_id ?? null;
            $knowledgeId     = $request->knowledge_id ?? null;
            $knowledgeItemId = $request->knowledge_item_id ?? null;
            $tagTexts        = $request->tags ?? '';

            // 如果没有提供标签，先创建笔记，然后异步生成标签,需要排除会议笔记
            $needsAITags = ! $tagTexts;
            if ($type == BailianArticleTypeEnum::MEETING->value) {
                return $request->kernel->error('此接口不能添加会议录音笔记');
                $needsAITags = false;
            }

            if ($needsAITags) {
                $tagTexts = [];
            }

            // 创建临时文章实例来生成描述
            $tempArticle = new BailianArticle([
                'content' => $content,
                'type'    => $type,
            ]);

            // 生成描述
            $description = $tempArticle->generateDescription();
            $content     = $tempArticle->stripHtmlTagsOfContent();
            $contentText = $content['data'] ?? [];
            $data        = [
                'user_id'     => $user->id,
                'title'       => $title,
                'content'     => $content,
                'description' => $description,
                'type'        => $type,
                'parent_id'   => $parentId,
            ];

            $article = BailianArticle::create($data);

            if ($tagTexts) {
                $article->setTags($user, $tagTexts);
            }

            if ($knowledgeId && is_null($parentId)) {
                // 快速验证知识库存在性和权限
                $knowledge = BailianKnowledge::find($request->knowledge_id);
                if (! $knowledge) {
                    throw new ValidatorException('知识库不存在');
                }
                if (! $knowledge->canSetData($user)) {
                    throw new ValidatorException('您没有权限将此笔记加入知识库');
                }

                // 使用策略模式立即创建本地知识库项目记录
                $strategy = KnowledgeItemStrategyFactory::createForModel($article);
                $strategy->addToKnowledge($article, $knowledge, '', [
                    'parent_id' => $knowledgeItemId,
                    'user_id'   => $user->id,
                ]);
            }

            if ($article->isChildArticle()) {
                $articleToUpdate = $article->getArticleToUpdateInKnowledge();
                if ($articleToUpdate) {
                    // 延迟到事务提交后再触发，确保子笔记内容已保存
                    DB::afterCommit(function () use ($articleToUpdate) {
                        ArticleUpdateJob::dispatch($articleToUpdate);
                    });
                }
            }

            DB::commit();

            if ($needsAITags) {
                // 异步生成标签
                dispatch(new GenerateArticleTagsJob($article, $contentText));
            }

            // 检查是否为会议类型，如果是则提交总结任务
            if ($type === BailianArticleTypeEnum::MEETING->value) {
                $meetingSummaryService = new MeetingSummaryService();
                if ($meetingSummaryService->needsGenerateSummary($article)) {
                    $summaryResult = $meetingSummaryService->submitMeetingSummaryTask($article);

                    $articleData                 = new ArticleResource($article);
                    $articleData                 = $articleData->toArray($request);
                    $articleData['summary_task'] = $summaryResult;

                    return $request->kernel->success($articleData);
                }
            }

            return $request->kernel->success(new ArticleResource($article));
        } catch (Exception $e) {
            DB::rollBack();
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * Notes: 添加会议笔记
     *
     * @Author: 玄尘
     * @Date: 2025/6/23 17:37
     */
    public function meeting(CreateArticleRequest $request)
    {
        try {
            DB::beginTransaction();
            $user            = $request->kernel->user();
            $title           = $request->title;
            $content         = $request->details;
            $type            = $request->type;
            $parentId        = $request->parent_id ?? null;
            $knowledgeId     = $request->knowledge_id ?? null;
            $knowledgeItemId = $request->knowledge_item_id ?? null;
            $tagTexts        = $request->tags ?? '';

            // 创建临时文章实例来生成描述
            $tempArticle = new BailianArticle([
                'content' => $content,
                'type'    => $type,
            ]);

            // 生成描述
            $description = $tempArticle->generateDescription();
            $content     = $tempArticle->stripHtmlTagsOfContent();
            $data        = [
                'user_id'     => $user->id,
                'title'       => $title,
                'content'     => $content,
                'description' => $description,
                'type'        => $type,
                'parent_id'   => $parentId,
            ];

            $article = BailianArticle::create($data);

            if ($knowledgeId && is_null($parentId)) {
                // 快速验证知识库存在性和权限
                $knowledge = BailianKnowledge::find($request->knowledge_id);
                if (! $knowledge) {
                    throw new ValidatorException('知识库不存在');
                }
                if (! $knowledge->canSetData($user)) {
                    throw new ValidatorException('您没有权限将此笔记加入知识库');
                }

                // 使用策略模式创建本地知识库项目记录
                // 对于会议笔记，不会立即触发同步事件，需要等会议总结完成后手动触发
                $strategy = KnowledgeItemStrategyFactory::createForModel($article);
                $strategy->addToKnowledge($article, $knowledge, '', [
                    'parent_id' => $knowledgeItemId,
                    'user_id'   => $user->id,
                ]);
            }

            DB::commit();

            // 检查是否需要生成会议总结
            $meetingSummaryService = new MeetingSummaryService();
            if ($meetingSummaryService->needsGenerateSummary($article)) {
                // 提交会议总结任务
                $meetingSummaryService->submitMeetingSummaryTask($article);
            }

            return $request->kernel->success(new ArticleResource($article));
        } catch (Exception $e) {
            DB::rollBack();
            \Log::info('创建会议笔记失败', [$e->getMessage()]);
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * 查询会议总结状态
     *
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function queryMeetingSummary(Request $request)
    {
        $request->validate([
            'article_id' => 'required|integer|exists:bailian_articles,id'
        ]);

        $user    = $request->kernel->user();
        $article = BailianArticle::findOrFail($request->article_id);

        // 检查权限
        if (! $article->isMy($user)) {
            return $request->kernel->error('无权限查看此笔记', 403);
        }

        $meetingSummaryService = new MeetingSummaryService();
        $result                = $meetingSummaryService->queryTaskStatus($article);

        if ($result['success']) {
            return $request->kernel->success($result);
        } else {
            return $request->kernel->error($result['message']);
        }
    }

    /**
     * 流式返回会议总结内容
     *
     * @param  Request  $request
     * @return StreamedResponse
     */
    public function streamMeetingSummary(Request $request): StreamedResponse
    {
        $request->validate([
            'article_id' => 'required|integer|exists:bailian_articles,id'
        ]);

        $user    = $request->kernel->user();
        $article = BailianArticle::find($request->article_id);

        // 检查权限
        if (! $article->isMy($user)) {
            return new StreamedResponse(function () {
                echo 'data: '.json_encode([
                        'type'    => 'error',
                        'message' => '无权限查看此笔记'
                    ], JSON_UNESCAPED_UNICODE)."\n\n";
            });
        }

        $meetingSummaryService = new MeetingSummaryService();
        return $meetingSummaryService->streamMeetingSummary($article);
    }

    /**
     * 更新文章
     *
     * @param  \App\Http\Requests\Bailian\Article\UpdateArticleRequest  $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function update(UpdateArticleRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $id       = $request->id;
            $title    = $request->title;
            $content  = $request->details;
            $type     = $request->type;
            $parentId = $request->parent_id ?? null;
            $tags     = $request->tags ?? '';

            $user = $request->kernel->user();

            $article = BailianArticle::find($id);
            if (! $article) {
                throw new ValidatorException('文章不存在');
            }

            if (! $article->canEdit($user)) {
                throw new ValidatorException('您没有权限编辑此文章');
            }

            $originalContent = $article->content;

            // 创建临时文章实例来生成描述
            $tempArticle = new BailianArticle([
                'content' => $content,
                'type'    => $type,
            ]);

            $description = $tempArticle->generateDescription();
            $content     = $tempArticle->stripHtmlTagsOfContent();

            $data = [
                'title'       => $title,
                'content'     => $content,
                'description' => $description,
                'type'        => $type,
                'parent_id'   => $parentId,
            ];

            $article->update(array_filter($data));

            if ($originalContent != $content) {
                $articleToUpdate = $article->getArticleToUpdateInKnowledge();
                if ($articleToUpdate) {
                    // 延迟到事务提交后再触发，确保子笔记内容已保存
                    DB::afterCommit(function () use ($articleToUpdate) {
                        ArticleUpdateJob::dispatch($articleToUpdate);
                    });
                }
            }
            if ($tags) {
                $article->setTags($user, $tags);
            }

            DB::commit();
            return $request->kernel->success(new ArticleResource($article));
        } catch (Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * 删除文章
     *
     * @param  Request  $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function delete(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $request->kernel->validate([
                'id' => ['required', 'exists:bailian_articles,id'],
            ], [
                'id.required' => '笔记ID不能为空',
                'id.exists'   => '笔记不存在',
            ]);

            $id   = $request->id;
            $user = $request->kernel->user();

            $article = BailianArticle::find($id);

            if (! $article->canDelete($user)) {
                $reason = $article->getDeleteBlockReason($user);
                throw new ValidatorException($reason ?: '您没有权限删除此笔记');
            }

            $article->delete();

            DB::commit();
            return $request->kernel->success('删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * 获取文章类型列表
     *
     * @param  Request  $request
     * @return JsonResponse
     * @throws \Laravel\Octane\Exceptions\DdException
     */
    public function types(Request $request): JsonResponse
    {
        return $request->kernel->success(BailianArticleTypeEnum::forSelectToString());
    }

    //加入知识库
    public function joinKnowledge(AddToKnowledgeRequest $request)
    {
        try {
            DB::beginTransaction();

            $id                = $request->article_id;
            $user              = $request->kernel->user();
            $knowledge_item_id = $request->knowledge_item_id ?? null;
            $knowledgeId       = $request->knowledge_id;

            $article = BailianArticle::find($id);
            if ($article->isChildArticle()) {
                throw new ValidatorException('只有主笔记才能加入知识库，子笔记会自动包含在主笔记中');
            }
            if (! $article->canMoveKnowledge($user)) {
                throw new ValidatorException('您没有权限将此笔记加入知识库或者此笔记已经加入知识库');
            }

            $knowledge = BailianKnowledge::find($knowledgeId);
            if (! $knowledge->canSetData($user)) {
                throw new ValidatorException('您没有权限将此笔记加入知识库');
            }

            // 使用策略模式立即创建本地知识库项目记录
            $strategy      = KnowledgeItemStrategyFactory::createForModel($article);
            $knowledgeItem = $strategy->addToKnowledge($article, $knowledge, '', [
                'parent_id' => $knowledge_item_id,
                'user_id'   => $user->id,
            ]);

            // 异步同步通过事件自动处理

            DB::commit();
            return $request->kernel->success('加入成功');
        } catch (Exception $e) {
            DB::rollBack();
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * Notes: 批量加入知识库
     *
     * @Author: 玄尘
     * @Date: 2025/5/29 15:55
     * @param  \App\Http\Requests\Bailian\Article\BatchAddToKnowledgeRequest  $request
     * @return mixed
     * @throws \Throwable
     */
    public function batchJoinKnowledge(BatchAddToKnowledgeRequest $request)
    {
        try {
            DB::beginTransaction();

            $user            = $request->kernel->user();
            $articleIds      = $request->article_ids;
            $knowledgeId     = $request->knowledge_id;
            $knowledgeItemId = $request->knowledge_item_id ?? null;

            // 验证知识库权限
            $knowledge = BailianKnowledge::find($knowledgeId);
            if (! $knowledge->canSetData($user)) {
                throw new ValidatorException('您没有权限将这些笔记加入知识库');
            }
            if ($knowledgeItemId) {
                $knowledgeItem = BailianKnowledgeItem::find($knowledgeItemId);
                if (! $knowledgeItem->isDirectory()) {
                    throw new ValidatorException('上级必须是目录');
                }
            }

            // 获取所有文章并验证权限
            $articles = BailianArticle::ofUser($user)->whereIn('id', $articleIds)->get();
            foreach ($articles as $article) {
                if ($article->isChildArticle()) {
                    throw new ValidatorException('只有主笔记才能加入知识库，子笔记会自动包含在主笔记中');
                }
                if (! $article->canMoveKnowledge($user)) {
                    throw new ValidatorException("只有主笔记才能加入知识库，{$article->getTitle()}是子笔记或已在知识库中");
                }
            }

            // 批量加入知识库 - 使用策略模式
            foreach ($articles as $article) {
                // 使用策略模式立即创建本地知识库项目记录
                $strategy      = KnowledgeItemStrategyFactory::createForModel($article);
                $knowledgeItem = $strategy->addToKnowledge($article, $knowledge, '', [
                    'parent_id' => $knowledgeItemId,
                    'user_id'   => $user->id,
                ]);
                // 异步同步通过事件自动处理
            }

            DB::commit();
            return $request->kernel->success('批量加入成功');
        } catch (Exception $e) {
            DB::rollBack();
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * 设置文章标签
     *
     * @param  SetTagRequest  $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function setTag(SetTagRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $articleId = $request->article_id;
            $tagIds    = $request->tag_ids;
            $user      = $request->kernel->user();

            $article = BailianArticle::find($articleId);
            if (! $article) {
                throw new ValidatorException('文章不存在');
            }

            if (! $article->canEdit($user)) {
                throw new ValidatorException('您没有权限编辑此文章');
            }

            // 验证标签是否属于当前用户
            $tags = BailianTag::whereIn('id', $tagIds)->get();
            foreach ($tags as $tag) {
                if ($tag->user_id !== $user->id) {
                    throw new ValidatorException('您没有权限使用此标签');
                }
            }

            // 更新文章和标签的关联关系
            $article->tags()->sync($tagIds);

            DB::commit();
            return $request->kernel->success('设置标签成功');
        } catch (Exception $e) {
            DB::rollBack();
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * 删除文章标签
     *
     * @param  DeleteTagRequest  $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function deleteTag(DeleteTagRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $articleId = $request->article_id;
            $tagId     = $request->tag_id;
            $user      = $request->kernel->user();

            $article = BailianArticle::find($articleId);

            if (! $article->canEdit($user)) {
                throw new ValidatorException('您没有权限编辑此文章');
            }

            // 验证标签是否属于当前用户
            $tag = BailianTag::find($tagId);
            if ($tag->user_id !== $user->id) {
                throw new ValidatorException('您没有权限使用此标签');
            }

            // 删除文章和标签的关联关系
            $article->tags()->detach($tagId);

            DB::commit();
            return $request->kernel->success('删除标签成功');
        } catch (Exception $e) {
            DB::rollBack();
            return $request->kernel->error($e->getMessage());
        }
    }

    public function listByTag(Request $request)
    {
        $request->kernel->validate([
            'tag_id' => ['required', 'exists:bailian_tags,id'],
        ], [
            'tag_id.required' => '标签ID不能为空',
            'tag_id.exists'   => '标签不存在',
        ]);
        $user = $request->kernel->user();
        $tag  = BailianTag::find($request->tag_id);

        $articles = BailianArticle::query()
            ->ofUser($user)
            ->whereHas('tags', function ($query) use ($request) {
                $query->where('bailian_tags.id', $request->tag_id);
            })
            ->get();

        $data = [
            'tag'      => new TagResource($tag),
            'articles' => ArticleResource::collection($articles),
        ];

        return $request->kernel->success($data);
    }

    /**
     * 重试同步失败的知识库项目
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function retrySyncToKnowledge(Request $request): JsonResponse
    {
        try {
            $request->kernel->validate([
                'article_id' => ['required', 'exists:bailian_articles,id'],
            ], [
                'article_id.required' => '文章ID不能为空',
                'article_id.exists'   => '文章不存在',
            ]);

            $user    = $request->kernel->user();
            $article = BailianArticle::find($request->article_id);

            if (! $article->canEdit($user)) {
                throw new ValidatorException('您没有权限操作此文章');
            }

            $knowledgeItem = $article->knowledgeItem;
            if (! $knowledgeItem) {
                throw new ValidatorException('文章不在知识库中');
            }

            if (! $knowledgeItem->isSyncFailed()) {
                throw new ValidatorException('只能重试同步失败的项目');
            }

            // 重置状态并重新同步
            $knowledgeItem->update([
                'sync_status' => ItemSyncStatusEnum::PENDING,
                'sync_error'  => null,
            ]);

            // 重新调度同步任务（用户主动重试，立即执行）
            SyncKnowledgeItemToAlibabaJob::dispatch($knowledgeItem);

            return $request->kernel->success('重试同步已启动');
        } catch (Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }

}
