<?php

namespace App\Http\Controllers\Api\CallBack;

use App\Http\Controllers\Controller;
use App\Models\BailianArticle;
use App\Services\MeetingSummaryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class XfYunCkmController extends Controller
{

    /**
     * 处理讯飞CKM回调
     */
    public function index(Request $request)
    {
        $data = $request->all();

        Log::info('收到讯飞CKM回调', ['callback_data' => $data]);

        try {
            // 验证回调数据
            if (! isset($data['taskId'])) {
                Log::warning('讯飞CKM回调缺少taskId', ['data' => $data]);
                return response()->json(['success' => false, 'message' => '缺少taskId']);
            }

            $taskId = $data['taskId'];

            // 查找对应的文章
            $article = BailianArticle::where('xfyun_task_id', $taskId)->first();
            if (! $article) {
                Log::warning('未找到对应的文章', ['task_id' => $taskId]);
                return response()->json(['success' => false, 'message' => '未找到对应的文章']);
            }

            // 直接调用MeetingSummaryService处理，无论成功失败都由Service统一处理
            $meetingSummaryService = new MeetingSummaryService();
            $result                = $meetingSummaryService->queryTaskStatus($article);

            Log::info('讯飞CKM回调处理完成', [
                'article_id' => $article->id,
                'task_id'    => $taskId,
                'success'    => $result['success'],
                'message'    => $result['message'] ?? ''
            ]);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('处理讯飞CKM回调异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data'  => $data
            ]);

            return response()->json(['success' => false, 'message' => '处理回调失败']);
        }
    }

}
