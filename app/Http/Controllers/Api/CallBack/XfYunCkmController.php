<?php

namespace App\Http\Controllers\Api\CallBack;

use App\Enums\Bailian\ArticleProcessingStatusEnum;
use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Http\Controllers\Controller;
use App\Jobs\BaiLian\GenerateArticleTagsJob;
use App\Models\BailianArticle;
use App\Models\BailianKnowledge;
use App\Services\KnowledgeItemStrategies\KnowledgeItemStrategyFactory;
use App\Services\MeetingSummaryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class XfYunCkmController extends Controller
{
    private MeetingSummaryService $meetingSummaryService;

    public function __construct()
    {
        $this->meetingSummaryService = new MeetingSummaryService();
    }

    /**
     * 处理讯飞CKM回调
     */
    public function index(Request $request)
    {
        $data = $request->all();

        Log::info('收到讯飞CKM回调', ['callback_data' => $data]);

        try {
            // 验证回调数据
            if (!isset($data['taskId'])) {
                Log::warning('讯飞CKM回调缺少taskId', ['data' => $data]);
                return response()->json(['success' => false, 'message' => '缺少taskId']);
            }

            $taskId = $data['taskId'];

            // 查找对应的文章
            $article = BailianArticle::where('xfyun_task_id', $taskId)->first();
            if (!$article) {
                Log::warning('未找到对应的文章', ['task_id' => $taskId]);
                return response()->json(['success' => false, 'message' => '未找到对应的文章']);
            }

            // 检查任务状态
            if (isset($data['status']) && $data['status'] === 'completed') {
                $this->handleTaskCompleted($article, $data);
            } elseif (isset($data['status']) && $data['status'] === 'failed') {
                $this->handleTaskFailed($article, $data);
            }

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            Log::error('处理讯飞CKM回调异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return response()->json(['success' => false, 'message' => '处理回调失败']);
        }
    }
