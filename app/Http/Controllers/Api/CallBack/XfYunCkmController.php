<?php

namespace App\Http\Controllers\Api\CallBack;

use App\Http\Controllers\Controller;
use App\Models\BailianArticle;
use App\Services\MeetingSummaryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class XfYunCkmController extends Controller
{
    private MeetingSummaryService $meetingSummaryService;

    public function __construct()
    {
        $this->meetingSummaryService = new MeetingSummaryService();
    }

    /**
     * 处理讯飞CKM回调
     */
    public function index(Request $request)
    {
        $data = $request->all();

        Log::info('收到讯飞CKM回调', ['callback_data' => $data]);

        try {
            // 验证回调数据
            if (!isset($data['taskId'])) {
                Log::warning('讯飞CKM回调缺少taskId', ['data' => $data]);
                return response()->json(['success' => false, 'message' => '缺少taskId']);
            }

            $taskId = $data['taskId'];

            // 查找对应的文章
            $article = BailianArticle::where('xfyun_task_id', $taskId)->first();
            if (!$article) {
                Log::warning('未找到对应的文章', ['task_id' => $taskId]);
                return response()->json(['success' => false, 'message' => '未找到对应的文章']);
            }

            // 检查任务状态
            if (isset($data['status']) && $data['status'] === 'completed') {
                $this->handleTaskCompleted($article, $data);
            } elseif (isset($data['status']) && $data['status'] === 'failed') {
                $this->handleTaskFailed($article, $data);
            }

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            Log::error('处理讯飞CKM回调异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return response()->json(['success' => false, 'message' => '处理回调失败']);
        }
    }

    /**
     * 处理任务完成
     */
    private function handleTaskCompleted(BailianArticle $article, array $data): void
    {
        Log::info('处理会议总结任务完成', ['article_id' => $article->id, 'task_id' => $article->xfyun_task_id]);

        try {
            // 查询并保存会议总结结果
            $queryResult = $this->meetingSummaryService->queryTaskStatus($article);

            if ($queryResult['success']) {
                Log::info('会议总结任务处理完成', ['article_id' => $article->id]);
            } else {
                $this->handleTaskFailed($article, ['error' => $queryResult['message'] ?? '查询结果失败']);
            }

        } catch (\Exception $e) {
            Log::error('处理会议总结任务完成异常', [
                'article_id' => $article->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->handleTaskFailed($article, ['error' => $e->getMessage()]);
        }
    }

    /**
     * 处理任务失败
     */
    private function handleTaskFailed(BailianArticle $article, array $data): void
    {
        Log::warning('会议总结任务失败', [
            'article_id' => $article->id,
            'task_id' => $article->xfyun_task_id,
            'error_data' => $data
        ]);

        $errorMessage = $data['error'] ?? $data['message'] ?? '未知错误';

        $article->update([
            'processing_status' => \App\Enums\Bailian\ArticleProcessingStatusEnum::FAILED,
            'processing_completed_at' => now(),
            'processing_error' => $errorMessage
        ]);
    }
